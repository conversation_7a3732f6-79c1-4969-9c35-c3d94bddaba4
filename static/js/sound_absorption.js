/**
 * 垂直入射法吸音系数查询功能
 */

class SoundAbsorptionQuery {
    constructor() {
        this.chart = null;
        this.comparisonChart = null;
        this.currentData = null;
        this.availableWeights = [];
        this.resizeListenerAdded = false;
        this.comparisonResizeListenerAdded = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadParts();
    }

    bindEvents() {
        // 零件选择事件
        $('#part-select').on('change', () => {
            this.onPartChange();
        });

        // 材料选择事件
        $('#material-select').on('change', () => {
            this.onMaterialChange();
        });

        // 克重选择事件
        $('#weight-select').on('change', () => {
            this.onWeightChange();
        });

        // 查询按钮
        $('#search-btn').on('click', () => {
            this.searchData();
        });

        // 多克重对比按钮
        $('#multi-compare-btn').on('click', () => {
            this.showMultiWeightModal();
        });

        // 确认对比按钮
        $('#confirm-comparison-btn').on('click', () => {
            this.performMultiWeightComparison();
        });

        // 查看测试附图按钮
        $('#view-image-btn').on('click', () => {
            this.viewTestImage();
        });

        // 导出数据按钮
        $('#export-btn').on('click', () => {
            this.exportData();
        });

        // 导出对比数据按钮
        $('#export-comparison-btn').on('click', () => {
            this.exportComparisonData();
        });

        // 克重复选框变化事件
        $(document).on('change', '#weight-checkboxes input[type="checkbox"]', () => {
            this.updateComparisonButton();
        });
    }

    async loadParts() {
        try {
            const response = await fetch('/sound_absorption/api/parts');
            const result = await response.json();
            
            if (result.code) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const select = $('#part-select');
        select.empty().append('<option value="">请选择零件</option>');
        
        parts.forEach(part => {
            select.append(`<option value="${part.name}">${part.name}</option>`);
        });
    }

    async onPartChange() {
        const partName = $('#part-select').val();
        
        // 重置后续选择
        $('#material-select').prop('disabled', true).empty().append('<option value="">请先选择零件</option>');
        $('#weight-select').prop('disabled', true).empty().append('<option value="">请先选择材料</option>');
        $('#search-btn').prop('disabled', true);
        $('#multi-compare-btn').prop('disabled', true);
        this.hideResults();

        if (!partName) return;

        try {
            const response = await fetch(`/sound_absorption/api/materials?part_name=${encodeURIComponent(partName)}`);
            const result = await response.json();
            
            if (result.code) {
                this.populateMaterialSelect(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载材料列表失败: ' + error.message);
        }
    }

    populateMaterialSelect(materials) {
        const select = $('#material-select');
        select.empty().append('<option value="">请选择材料</option>');
        
        materials.forEach(material => {
            select.append(`<option value="${material.name}">${material.name}</option>`);
        });
        
        select.prop('disabled', false);
    }

    async onMaterialChange() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        
        // 重置克重选择
        $('#weight-select').prop('disabled', true).empty().append('<option value="">请先选择材料</option>');
        $('#search-btn').prop('disabled', true);
        $('#multi-compare-btn').prop('disabled', true);
        this.hideResults();

        if (!partName || !materialName) return;

        try {
            const response = await fetch(`/sound_absorption/api/weights?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}`);
            const result = await response.json();
            
            if (result.code) {
                this.populateWeightSelect(result.data);
                this.availableWeights = result.data;
            } else {
                this.showError('加载克重列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载克重列表失败: ' + error.message);
        }
    }

    populateWeightSelect(weights) {
        const select = $('#weight-select');
        select.empty().append('<option value="">请选择克重</option>');
        
        weights.forEach(weight => {
            select.append(`<option value="${weight.weight}">${weight.display}</option>`);
        });
        
        select.prop('disabled', false);
        
        // 如果有多个克重，启用多克重对比按钮
        if (weights.length > 1) {
            $('#multi-compare-btn').prop('disabled', false);
        }
    }

    onWeightChange() {
        const weight = $('#weight-select').val();
        $('#search-btn').prop('disabled', !weight);
        this.hideResults();
    }

    async searchData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        if (!partName || !materialName || !weight) {
            this.showError('请选择完整的查询条件');
            return;
        }

        this.showLoading();

        try {
            const response = await fetch(`/sound_absorption/api/absorption_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`);
            const result = await response.json();
            
            if (result.code) {
                this.currentData = result.data;
                this.displayResults(result.data);
            } else {
                this.showError('查询失败: ' + result.message);
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        this.hideEmptyState();
        this.hideComparisonResults();

        // 显示基础信息
        this.displayBasicInfo(data.basic_info);

        // 显示数据表格
        this.displayDataTable(data.table_data);

        // 先显示结果卡片，确保容器可见
        $('#results-card').show();

        // 延迟显示图表，确保容器已完全渲染
        setTimeout(() => {
            this.displayChart(data.chart_data);
        }, 50);
    }

    displayBasicInfo(basicInfo) {
        const container = $('#basic-info');
        container.empty();
        
        const fields = [
            { label: '零件名称', value: basicInfo.part_name },
            { label: '材料名称', value: basicInfo.material_name },
            { label: '材料厂家', value: basicInfo.material_manufacturer || '-' },
            { label: '测试机构', value: basicInfo.test_institution || '-' },
            { label: '厚度', value: basicInfo.thickness ? `${basicInfo.thickness}mm` : '-' },
            { label: '克重', value: `${basicInfo.weight}g/m²` },
            { label: '测试日期', value: basicInfo.test_date || '-' },
            { label: '测试工程师', value: basicInfo.test_engineer || '-' },
            { label: '测试地点', value: basicInfo.test_location || '-' }
        ];
        
        fields.forEach(field => {
            container.append(`
                <div class="col-md-4 col-sm-6 mb-2">
                    <strong>${field.label}:</strong> ${field.value}
                </div>
            `);
        });
    }

    displayDataTable(tableData) {
        const thead = $('#data-table thead');
        const tbody = $('#data-table tbody');

        // 清空现有内容
        tbody.empty();

        // 生成表头 - 频率作为列标题
        let headerRow = '<tr><th style="width: 60px; min-width: 60px;">频率</th>';
        tableData.forEach(row => {
            headerRow += `<th class="text-center" style="width: 80px; min-width: 80px;">${row.frequency}</th>`;
        });
        headerRow += '</tr>';
        thead.html(headerRow);

        // 生成测试值行
        let testValueRow = '<tr><td><strong>测试</strong></td>';
        tableData.forEach(row => {
            testValueRow += `<td class="text-center">${row.test_value !== null ? row.test_value.toFixed(3) : '-'}</td>`;
        });
        testValueRow += '</tr>';
        tbody.append(testValueRow);

        // 生成目标值行
        let targetValueRow = '<tr><td><strong>目标</strong></td>';
        tableData.forEach(row => {
            targetValueRow += `<td class="text-center">${row.target_value !== null ? row.target_value.toFixed(3) : '-'}</td>`;
        });
        targetValueRow += '</tr>';
        tbody.append(targetValueRow);
    }

    displayChart(chartData) {
        const container = document.getElementById('chart-container');

        if (this.chart) {
            this.chart.dispose();
        }

        // 确保容器可见后再初始化图表
        setTimeout(() => {
            // 检查容器是否可见
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Chart container is not visible, retrying...');
                setTimeout(() => this.displayChart(chartData), 100);
                return;
            }

            this.chart = echarts.init(container);

            const option = {
                title: {
                    text: '吸音系数曲线图',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `<strong>${params[0].axisValue}</strong><br/>`;
                        params.forEach(param => {
                            const value = param.value !== null ? param.value.toFixed(3) : '-';
                            result += `${param.marker}${param.seriesName}: ${value}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: ['测试值', '目标值'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: chartData.frequencies,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '吸音系数',
                    min: 0,
                    max: 1
                },
                series: [
                    {
                        name: '测试值',
                        type: 'line',
                        data: chartData.test_values,
                        lineStyle: { color: '#007bff', width: 2 },
                        itemStyle: { color: '#007bff' },
                        symbol: 'circle',
                        symbolSize: 6
                    },
                    {
                        name: '目标值',
                        type: 'line',
                        data: chartData.target_values,
                        lineStyle: { color: '#28a745', type: 'dashed', width: 2 },
                        itemStyle: { color: '#28a745' },
                        symbol: 'triangle',
                        symbolSize: 6
                    }
                ]
            };

            this.chart.setOption(option);

            // 强制触发resize以确保图表正确显示
            setTimeout(() => {
                if (this.chart) {
                    this.chart.resize();
                }
            }, 50);

        }, 100);

        // 响应式调整 - 移除重复的事件监听器
        if (!this.resizeListenerAdded) {
            window.addEventListener('resize', () => {
                if (this.chart) {
                    this.chart.resize();
                }
            });
            this.resizeListenerAdded = true;
        }
    }

    showMultiWeightModal() {
        const container = $('#weight-checkboxes');
        container.empty();
        
        this.availableWeights.forEach(weight => {
            container.append(`
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${weight.weight}" id="weight-${weight.weight}">
                    <label class="form-check-label" for="weight-${weight.weight}">
                        ${weight.display}
                    </label>
                </div>
            `);
        });
        
        $('#multiWeightModal').modal('show');
    }

    updateComparisonButton() {
        const checkedCount = $('#weight-checkboxes input[type="checkbox"]:checked').length;
        $('#confirm-comparison-btn').prop('disabled', checkedCount < 2);
    }

    async performMultiWeightComparison() {
        const selectedWeights = [];
        $('#weight-checkboxes input[type="checkbox"]:checked').each(function() {
            selectedWeights.push(parseFloat($(this).val()));
        });

        if (selectedWeights.length < 2) {
            this.showError('请至少选择2个克重进行对比');
            return;
        }

        $('#multiWeightModal').modal('hide');
        this.showLoading();

        try {
            const response = await fetch('/sound_absorption/api/multi_weight_comparison', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    part_name: $('#part-select').val(),
                    material_name: $('#material-select').val(),
                    weights: selectedWeights
                })
            });
            
            const result = await response.json();
            
            if (result.code) {
                this.displayComparisonResults(result.data, selectedWeights);
            } else {
                this.showError('多克重对比失败: ' + result.message);
            }
        } catch (error) {
            this.showError('多克重对比失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    displayComparisonResults(data, weights) {
        this.hideEmptyState();
        this.hideResults();

        // 显示对比信息
        this.displayComparisonInfo(data.basic_info, weights);

        // 显示对比表格
        this.displayComparisonTable(data.table_data, weights);

        $('#comparison-count').text(`${weights.length} 个克重`);

        // 先显示对比结果卡片，确保容器可见
        $('#comparison-results-card').show();

        // 延迟显示对比图表，确保容器已完全渲染
        setTimeout(() => {
            this.displayComparisonChart(data.chart_data);
        }, 50);
    }

    displayComparisonInfo(basicInfo, weights) {
        const container = $('#comparison-info');
        container.empty();
        
        const weightsText = weights.map(w => `${w}g/m²`).join(', ');
        
        container.append(`
            <div class="col-md-4 mb-2">
                <strong>零件名称:</strong> ${basicInfo.part_name}
            </div>
            <div class="col-md-4 mb-2">
                <strong>材料名称:</strong> ${basicInfo.material_name}
            </div>
            <div class="col-md-4 mb-2">
                <strong>对比克重:</strong> ${weightsText}
            </div>
        `);
    }

    displayComparisonTable(tableData, weights) {
        const table = $('#comparison-table');
        const thead = table.find('thead');
        const tbody = table.find('tbody');

        // 构建表头 - 频率作为列标题
        thead.empty();
        let headerRow = '<tr><th class="sticky-column" style="width: 100px; min-width: 100px;">频率</th>';
        tableData.forEach(row => {
            headerRow += `<th class="text-center" style="width: 80px; min-width: 80px;">${row.frequency}</th>`;
        });
        headerRow += '</tr>';
        thead.append(headerRow);

        // 构建表格内容 - 每个克重的测试值和目标值作为行
        tbody.empty();

        weights.forEach(weight => {
            // 测试值行
            let testRow = `<td class="sticky-column"><strong>${weight}测试</strong></td>`;
            tableData.forEach(row => {
                const testValue = row[`test_${weight}`];
                testRow += `<td class="text-center">${testValue !== null && testValue !== undefined ? testValue.toFixed(3) : '-'}</td>`;
            });
            tbody.append(`<tr>${testRow}</tr>`);

            // 目标值行
            let targetRow = `<td class="sticky-column"><strong>${weight}目标</strong></td>`;
            tableData.forEach(row => {
                const targetValue = row[`target_${weight}`];
                targetRow += `<td class="text-center">${targetValue !== null && targetValue !== undefined ? targetValue.toFixed(3) : '-'}</td>`;
            });
            tbody.append(`<tr>${targetRow}</tr>`);
        });
    }

    displayComparisonChart(chartData) {
        const container = document.getElementById('comparison-chart-container');

        if (this.comparisonChart) {
            this.comparisonChart.dispose();
        }

        // 确保容器可见后再初始化图表
        setTimeout(() => {
            // 检查容器是否可见
            if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn('Comparison chart container is not visible, retrying...');
                setTimeout(() => this.displayComparisonChart(chartData), 100);
                return;
            }

            this.comparisonChart = echarts.init(container);

            const option = {
                title: {
                    text: '多克重吸音系数对比图',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = `<strong>${params[0].axisValue}</strong><br/>`;
                        params.forEach(param => {
                            const value = param.value !== null ? param.value.toFixed(3) : '-';
                            result += `${param.marker}${param.seriesName}: ${value}<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: chartData.series.map(s => s.name),
                    top: 30,
                    type: 'scroll'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '20%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: chartData.frequencies,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '吸音系数',
                    min: 0,
                    max: 1
                },
                series: chartData.series.map((series, index) => ({
                    ...series,
                    lineStyle: {
                        ...series.lineStyle,
                        width: 2
                    },
                    symbolSize: 6
                }))
            };

            this.comparisonChart.setOption(option);

            // 强制触发resize以确保图表正确显示
            setTimeout(() => {
                if (this.comparisonChart) {
                    this.comparisonChart.resize();
                }
            }, 50);

        }, 100);

        // 响应式调整 - 移除重复的事件监听器
        if (!this.comparisonResizeListenerAdded) {
            window.addEventListener('resize', () => {
                if (this.comparisonChart) {
                    this.comparisonChart.resize();
                }
            });
            this.comparisonResizeListenerAdded = true;
        }
    }

    async viewTestImage() {
        if (!this.currentData) {
            this.showError('请先查询数据');
            return;
        }

        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        try {
            const response = await fetch(`/sound_absorption/api/test_image?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`);
            const result = await response.json();

            if (result.code) {
                this.displayTestImage(result.data);
            } else {
                this.showError('获取测试图片失败: ' + result.message);
            }
        } catch (error) {
            this.showError('获取测试图片失败: ' + error.message);
        }
    }

    displayTestImage(imageInfo) {
        const container = $('#image-info');
        container.empty();

        const fields = [
            { label: '零件名称', value: imageInfo.part_name },
            { label: '材料名称', value: imageInfo.material_name },
            { label: '克重', value: imageInfo.weight },
//            { label: '测试日期', value: imageInfo.test_date || '-' },
//            { label: '测试工程师', value: imageInfo.test_engineer || '-' },
//            { label: '测试地点', value: imageInfo.test_location || '-' },
//            { label: '测试机构', value: imageInfo.test_institution || '-' }
        ];

        fields.forEach(field => {
            container.append(`
                <div class="row mb-1">
                    <div class="col-4"><strong>${field.label}:</strong></div>
                    <div class="col-8">${field.value}</div>
                </div>
            `);
        });

        if (imageInfo.remarks) {
            container.append(`
                <div class="row mb-1">
                    <div class="col-4"><strong>备注:</strong></div>
                    <div class="col-8">${imageInfo.remarks}</div>
                </div>
            `);
        }

        // 显示图片 - 修复图片路径处理
        const testImage = $('#test-image');
        const noImage = $('#no-image');

        if (imageInfo.test_image_path) {
            // 如果路径不是以http开头，则添加/static/uploads/前缀
            let imageSrc = imageInfo.test_image_path;
            if (!imageSrc.startsWith('http') && !imageSrc.startsWith('/static/')) {
                imageSrc = `/static/uploads/${imageSrc}`;
            }
            testImage.attr('src', imageSrc).show();
            noImage.hide();
        } else {
            testImage.hide();
            noImage.show();
        }

        $('#imageModal').modal('show');
    }

    async exportData() {
        const partName = $('#part-select').val();
        const materialName = $('#material-select').val();
        const weight = $('#weight-select').val();

        if (!partName || !materialName || !weight) {
            this.showError('请先查询数据');
            return;
        }

        try {
            const url = `/sound_absorption/api/export_data?part_name=${encodeURIComponent(partName)}&material_name=${encodeURIComponent(materialName)}&weight=${weight}`;
            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出数据失败: ' + error.message);
        }
    }

    async exportComparisonData() {
        const selectedWeights = [];
        $('#weight-checkboxes input[type="checkbox"]:checked').each(function() {
            selectedWeights.push(parseFloat($(this).val()));
        });

        if (selectedWeights.length < 2) {
            this.showError('请先进行多克重对比');
            return;
        }

        try {
            const response = await fetch('/sound_absorption/api/export_comparison', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    part_name: $('#part-select').val(),
                    material_name: $('#material-select').val(),
                    weights: selectedWeights
                })
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = response.headers.get('Content-Disposition')?.split('filename=')[1] || 'comparison_data.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                this.showError('导出对比数据失败');
            }
        } catch (error) {
            this.showError('导出对比数据失败: ' + error.message);
        }
    }

    showLoading() {
        $('#loading-indicator').show();
        $('#empty-state').hide();
        $('#results-card').hide();
        $('#comparison-results-card').hide();
    }

    hideLoading() {
        $('#loading-indicator').hide();
    }

    hideEmptyState() {
        $('#empty-state').hide();
    }

    hideResults() {
        $('#results-card').hide();
    }

    hideComparisonResults() {
        $('#comparison-results-card').hide();
    }

    showError(message) {
        // 这里可以使用Toast或其他通知组件
        alert('错误: ' + message);
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    new SoundAbsorptionQuery();
});
